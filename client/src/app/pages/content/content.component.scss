@use "../categories/category/category.component.scss";

h1 {
  font-weight: 600;
  font-size: 20px;
  padding: 15px 0;
}

.main_art-image {
  width: 100%;
  height: 456px;
  border-radius: 30px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 50px 0 65px 0;
  position: relative;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
  }

  .image-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg,
      var(--light-color, rgba(255, 255, 255, 0.9)) 0%,
      var(--selection, rgba(255, 248, 235, 0.9)) 50%,
      var(--pl-stop, rgba(252, 243, 220, 0.9)) 100%);
    backdrop-filter: blur(4px);
    border-radius: 30px;
    z-index: 2;

    app-loading-indicator {
      padding: 0;
    }
  }
}

.btn-like svg {
  width: 24px;
}

.content_wrap_ {
  display: flex;
  cursor: pointer;

  .social_par {
    font-family: Prata;
    font-weight: 400;
    font-size: 24px;
    line-height: 24px;
    color: var(--font-color1);
  }
}

.content_ {
  background-size: contain !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  margin: 0 20px 20px 0;

  &._telegram {
    background: var(--cover_telegram);
    width: 28px;
    height: 24px;
  }

  &._instagram {
    background: var(--cover_instagram);
    width: 24px;
    height: 24px;
  }

  &._phone {
    background: var(--cover_phone);
    width: 27px;
    height: 19px;
  }

  &._email {
    background: var(--cover_email);
    width: 24px;
    height: 23px;
  }
}

span {
  font-family: IBM_Plex_Sans;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  color: #fff;
}

.btn-like svg path {
  fill: transparent;
  stroke: #f3d593;
}

.btn-like.is-liked path {
  fill: red !important;
  stroke: none !important;
}

.btn-favourite svg {
  width: 20px;
  height: 20px;
}

.btn-favourite.in-favourites svg {
  fill: #ffc94a;
  stroke: none;
}

.icons_w span.text-color {
  color: var(--font-color1);
}

.content-first-chunk {
  //content-visibility: visible;
  //contain-intrinsic-size: auto 500px;
  //height: 300px;
  //overflow-y: auto;
}

.content-chunk {
  content-visibility: auto;
  contain-intrinsic-size: auto 500px;
}

.social {
  margin: 70px 0;
}

// Sticky actions bar for activity icons
.sticky-actions-bar {
  position: sticky;
  top: 108px; // Height of header on desktop
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 10px 0;

  // Adjust for mobile header height
  @media (max-width: 1100px) {
    top: 80px; // Height of header on mobile
  }
}

// .social a {
//   color: red;
//   display: block;
//   text-decoration: underline;
//   padding: 5px 0;
// }

.conts_title {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 24px;
  color: var(--font-color1);
  padding: 0 12px 12px 26px;
  border-bottom: 1px solid var(--text-color);
}

.conts_item {
  display: flex;
  align-items: center;
  font-family: Prata;
  font-weight: 400;
  font-size: 18px;
  line-height: 23px;
  color: var(--font-color1);
  padding: 12px 12px 12px 26px;
  min-height: 44px;
  word-break: break-all;

  &:hover {
    background-color: var(--selection);
  }
}

.tags a {
  display: block;
  text-decoration: underline;
  cursor: pointer;
}


.headers a {
  display: block;
  text-decoration: underline;
  cursor: pointer;
}

.headers {
  padding: 20px 0;
}

.similar-content-section {
  margin: 40px 0 20px 0;
}

.similar-items {
  display: flex;
  flex-wrap: wrap;
  margin: 70px -30px 0 0;
}

.similar-item-card {
  display: block;
  // border-radius: 8px;
  // overflow: hidden;
  // box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  // transition: transform 0.3s ease, box-shadow 0.3s ease;
  // background: #fff;
  // text-decoration: none;
  // color: inherit;
  margin-right: 30px;
  transition: all .3s;
}

.similar-item-card:hover {
  img {
    scale: 1.2;
    transition: all .3s;
  }

  .similar-item-title {
    color: rgba(42, 124, 187, 1);
  }
}

.similar-item-image {
  width: 290px;
  height: 200px;
  overflow: hidden;
  border-radius: 20px;
  position: relative;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
  }

  .image-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg,
      var(--light-color, rgba(255, 255, 255, 0.9)) 0%,
      var(--selection, rgba(255, 248, 235, 0.9)) 50%,
      var(--pl-stop, rgba(252, 243, 220, 0.9)) 100%);
    backdrop-filter: blur(4px);
    border-radius: 20px;
    z-index: 2;

    app-loading-indicator {
      padding: 0;
    }
  }
}

.similar-item-content {
  padding: 30px 0 0 0;
  width: 290px;
}

.similar-item-title {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 28px;
  color: var(--font-color1);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
}

.similar-item-excerpt {
  font-size: 14px;
  color: #666;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@media (max-width: 1008px) {
  .cal_w {
    margin-left: 0;
  }

  .hide_cont_md {
    display: none;
  }

  .content-context.contents_ {
    top: 35px;
    right: -60px;
    width: 400px;
    padding: 30px 0 25px 0;
  }

  .conts_title {
    font-size: 20px;
    line-height: 20px;
    padding: 0 12px 12px 37px;
  }

  .content-context.contents_::before {
    left: 82.5%;
  }

  .conts_item {
    font-size: 14px;
    line-height: 19px;
    padding: 12px 12px 12px 37px;
    min-height: 41px;
  }

  .main_art-image {
    height: 284px;
    border-radius: 15px;
    margin: 46px 0 48px 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .wrapper_line>div:not(.dec_head) {
    max-width: 580px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (max-width: 768px) {
  .icons_w {
    display: flex !important;
  }

  .content-context.contents_ {
    right: -20px;
  }

  .content-context.contents_::before {
    left: 92.5%;
  }
}

@media (max-width: 600px) {
  .content-context.contents_ {
    right: -8px;
    right: -6px;
    width: 305px;
    padding: 25px 0 20px 0;
  }

  .main_art-image {
    height: 226px;
    margin: 36px 0 36px 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .content-context.contents_::before {
    left: 90.5%;
  }

  .icons_w span.text-color {
    font-size: 12px;
  }

  .icons_w:not(.conts_r) {
    display: flex !important;
    margin-right: 15px;
  }

  .conts_r {
    margin-left: auto;
  }
}

@media (max-width: 450px) {
  .buttons a div.button_cont-wrap {
    font-size: 15px !important;
  }

  .html_wrap h1,
  .html_wrap h2,
  .html_wrap h3,
  .html_wrap h4,
  .html_wrap h5,
  .html_wrap h6 {
    margin: 20px 0 !important;
  }

  .middle_stripe {
    padding: 0 20px;
  }

  .main_art-image {
    height: 162px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .icons_w.hide_sm {
    display: none !important;
  }
}

@media (max-width: 350px) {
  .main_art-image {
    height: 142px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.responsive-video-container {
  position: relative;
  padding-bottom: 56.25%;
  /* 16:9 Aspect Ratio */
  height: 0;
  overflow: hidden;

  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
  }
}

.library-audio__list {
  border-radius: 10px;
  border: 1px solid var(--book_about);
  padding: 12px 0;
  .library-audio__list-inner {
    max-height: 204px;
    .library-audio {
      padding: 12px 20px;
      font-size: 16px;
      line-height: 20px;
      &.active-audio {
        background-color: var(--selection);
      }
      .audio-name {
        flex: 1 1 0;
      }
      .audio-time {
        font-size: 12px;
        line-height: 16px;
        text-align: right;
      }
    }
  }
}


//youtube ссылки массив
.youtube-links-container {
  margin: 30px 0;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 10px;
    margin-bottom: 10px;
  }
}

.video-wrapper {
  position: relative;
  width: 100%;
}

.is-paid-content {
  .responsive-video-container {
    iframe {
      filter: blur(8px);
      pointer-events: none;
    }
  }

  .paid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    border-radius: 8px;
    z-index: 10;
    padding: 20px;

    .paid-overlay-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;

      p {
        font-size: 1.1rem;
        max-width: 300px;
      }

      svg {
        color: #fff;
      }
    }
  }
}

.responsive-video-container {
  position: relative;
  width: 100%;

  iframe {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    min-height: 300px;
    border-radius: 8px;
  }
}

.purchase-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #dee2e6;
  border-radius: 12px;
  padding: 30px;
  margin: 30px 0;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

  .purchase-info {
    h3 {
      color: #2c5aa0;
      margin-bottom: 15px;
      font-size: 1.5rem;
      font-weight: 600;
    }

    .price-display {
      margin: 20px 0;

      .price {
        display: inline-block;
        background: #2c5aa0;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 1.2rem;
        font-weight: bold;
        margin: 0 5px;
      }
    }

    .purchase-btn {
      background: #2c5aa0;
      color: white;
      border: none;
      padding: 12px 30px;
      border-radius: 6px;
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:hover {
        background: #1e3f73;
      }
    }
  }
}

.access-denied {
  background: #fff3cd;
  border: 2px solid #ffeaa7;
  border-radius: 8px;
  padding: 30px;
  margin: 30px 0;
  text-align: center;

  p {
    color: #856404;
    font-size: 1.1rem;
    margin-bottom: 20px;
  }

  .btn {
    background: #2c5aa0;
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: #1e3f73;
    }
  }
}

@media (max-width: 768px) {
  .purchase-section {
    padding: 20px;
    margin: 20px 0;

    .purchase-info {
      h3 {
        font-size: 1.3rem;
      }

      .price-display .price {
        font-size: 1.1rem;
        padding: 6px 12px;
      }

      .purchase-btn {
        padding: 10px 25px;
        font-size: 1rem;
      }
    }
  }

  .access-denied {
    padding: 20px;
    margin: 20px 0;

    p {
      font-size: 1rem;
    }
  }
}
